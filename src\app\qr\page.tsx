"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  QrCode,
  Wifi,
  Upload,
  Download,
  Printer,
  Copy,
  AlertCircle,
  Loader2,
  Check,
} from "lucide-react";
import { toast } from "sonner";
import QRCode from "qrcode";

interface QRState {
  wifiQR: string;
  uploadQR: string;
  isGenerating: boolean;
  error: string;
  copiedWifi: boolean;
  copiedUpload: boolean;
}

interface WiFiFormData {
  ssid: string;
  password: string;
}

export default function QRAccessPage() {
  const [qrState, setQRState] = useState<QRState>({
    wifiQR: "",
    uploadQR: "",
    isGenerating: false,
    error: "",
    copiedWifi: false,
    copiedUpload: false,
  });

  const [wifiForm, setWifiForm] = useState<WiFiFormData>({
    ssid: "",
    password: "",
  });

  // Get network IP from server
  const getNetworkIP = async (): Promise<{
    baseURL: string;
    localIP: string;
  }> => {
    try {
      const response = await fetch("/api/network-ip");
      const data = await response.json();

      if (data.success) {
        return {
          baseURL: data.baseURL,
          localIP: data.localIP,
        };
      } else {
        throw new Error(data.message || "Failed to get network IP");
      }
    } catch (error) {
      console.warn("Could not detect network IP, using localhost:", error);
      return {
        baseURL: `${window.location.origin}`,
        localIP: "localhost",
      };
    }
  };

  // Generate both QR codes
  const generateQRCodes = async () => {
    if (!wifiForm.ssid.trim()) {
      toast.error("WiFi network name (SSID) is required");
      return;
    }

    setQRState((prev) => ({ ...prev, isGenerating: true, error: "" }));

    try {
      // Get network IP from server
      const { baseURL, localIP } = await getNetworkIP();

      // Generate WiFi QR code
      const wifiString = `WIFI:T:WPA;S:${wifiForm.ssid};P:${wifiForm.password};H:false;;`;
      const wifiQRDataURL = await QRCode.toDataURL(wifiString, {
        width: 300,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      // Generate Upload QR code with detected IP
      const uploadURL = `${baseURL}/upload`;

      const uploadQRDataURL = await QRCode.toDataURL(uploadURL, {
        width: 300,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      setQRState((prev) => ({
        ...prev,
        wifiQR: wifiQRDataURL,
        uploadQR: uploadQRDataURL,
        isGenerating: false,
        copiedWifi: false,
        copiedUpload: false,
      }));

      toast.success(
        localIP === "localhost"
          ? "QR codes generated! Note: Using localhost - mobile devices may not be able to access the upload URL"
          : `QR codes generated successfully! Upload URL: ${uploadURL}`
      );
    } catch (error) {
      console.error("Error generating QR codes:", error);
      setQRState((prev) => ({
        ...prev,
        error: "Failed to generate QR codes",
        isGenerating: false,
      }));
      toast.error("Failed to generate QR codes");
    }
  };

  // Download QR Code
  const downloadQR = (dataURL: string, filename: string) => {
    const link = document.createElement("a");
    link.download = filename;
    link.href = dataURL;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success(`${filename} downloaded successfully!`);
  };

  // Copy WiFi QR Code to clipboard
  const copyWifiQRToClipboard = async () => {
    if (!qrState.wifiQR) return;

    try {
      const response = await fetch(qrState.wifiQR);
      const blob = await response.blob();
      await navigator.clipboard.write([
        new ClipboardItem({ "image/png": blob }),
      ]);

      setQRState((prev) => ({ ...prev, copiedWifi: true }));
      toast.success("WiFi QR code copied to clipboard!");

      // Reset copied state after 2 seconds
      setTimeout(() => {
        setQRState((prev) => ({ ...prev, copiedWifi: false }));
      }, 2000);
    } catch (error) {
      console.error("Error copying WiFi QR code:", error);
      toast.error("Failed to copy WiFi QR code to clipboard");
    }
  };

  // Copy Upload QR Code to clipboard
  const copyUploadQRToClipboard = async () => {
    if (!qrState.uploadQR) return;

    try {
      const response = await fetch(qrState.uploadQR);
      const blob = await response.blob();
      await navigator.clipboard.write([
        new ClipboardItem({ "image/png": blob }),
      ]);

      setQRState((prev) => ({ ...prev, copiedUpload: true }));
      toast.success("Upload QR code copied to clipboard!");

      // Reset copied state after 2 seconds
      setTimeout(() => {
        setQRState((prev) => ({ ...prev, copiedUpload: false }));
      }, 2000);
    } catch (error) {
      console.error("Error copying Upload QR code:", error);
      toast.error("Failed to copy Upload QR code to clipboard");
    }
  };

  // Print QR Codes
  const printQRCodes = () => {
    const printWindow = window.open("", "_blank");
    if (!printWindow) return;

    const printContent = `
      <html>
        <head>
          <title>LDIS QR Access Codes</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            .qr-section { margin-bottom: 40px; text-align: center; }
            .qr-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
            .qr-image { max-width: 300px; height: auto; }
            .wifi-info { margin-top: 10px; font-size: 14px; color: #666; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <h1 style="text-align: center; color: oklch(0.5 0.15 240);">LDIS QR Access Codes</h1>
          ${
            qrState.wifiQR
              ? `
            <div class="qr-section">
              <div class="qr-title">WiFi Connection</div>
              <img src="${qrState.wifiQR}" alt="WiFi QR Code" class="qr-image" />
              <div class="wifi-info">
                <div>Network: ${wifiForm.ssid}</div>
              </div>
            </div>
          `
              : ""
          }
          ${
            qrState.uploadQR
              ? `
            <div class="qr-section">
              <div class="qr-title">Upload Documents</div>
              <img src="${qrState.uploadQR}" alt="Upload QR Code" class="qr-image" />
              <div class="wifi-info">Scan to access document upload page</div>
            </div>
          `
              : ""
          }
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
  };

  const handleWifiFormChange = (field: keyof WiFiFormData, value: string) => {
    setWifiForm((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2 flex items-center gap-3">
            <QrCode className="h-8 w-8 text-primary" />
            QR Access
          </h1>
          <p className="text-muted-foreground">
            Generate QR codes for WiFi connection and document upload access.
            Perfect for providing easy mobile access to LDIS functionality.
          </p>
        </div>

        <div className="grid gap-8 lg:grid-cols-2">
          {/* WiFi QR Code Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wifi className="h-5 w-5 text-primary" />
                WiFi Connection QR Code
              </CardTitle>
              <CardDescription>
                Generate a QR code that automatically connects mobile devices to
                your WiFi network.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="ssid">WiFi Network Name (SSID) *</Label>
                  <Input
                    id="ssid"
                    type="text"
                    placeholder="Enter WiFi network name"
                    value={wifiForm.ssid}
                    onChange={(e) =>
                      handleWifiFormChange("ssid", e.target.value)
                    }
                    disabled={qrState.isGenerating}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">WiFi Password</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter WiFi password (optional)"
                    value={wifiForm.password}
                    onChange={(e) =>
                      handleWifiFormChange("password", e.target.value)
                    }
                    disabled={qrState.isGenerating}
                  />
                </div>
              </div>

              {qrState.wifiQR && (
                <div className="space-y-4">
                  <Separator />
                  <div className="text-center">
                    <img
                      src={qrState.wifiQR}
                      alt="WiFi QR Code"
                      className="mx-auto border rounded-lg shadow-sm"
                    />
                    <p className="text-sm text-muted-foreground mt-2">
                      Scan with mobile device to connect to WiFi
                    </p>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyWifiQRToClipboard}
                      className="flex-1"
                    >
                      {qrState.copiedWifi ? (
                        <>
                          <Check className="h-4 w-4 mr-2" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4 mr-2" />
                          Copy
                        </>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        downloadQR(
                          qrState.wifiQR,
                          `wifi-qr-${wifiForm.ssid}.png`
                        )
                      }
                      className="flex-1"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Document Upload QR Code Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5 text-primary" />
                Document Upload QR Code
              </CardTitle>
              <CardDescription>
                Generate a QR code that links directly to the LDIS document
                upload page.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="p-4 bg-muted/50 rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    This QR code will link to: <br />
                    <code className="text-xs bg-background px-2 py-1 rounded">
                      {typeof window !== "undefined"
                        ? `${window.location.origin}/upload`
                        : "/upload"}
                    </code>
                  </p>
                </div>
              </div>

              {qrState.uploadQR && (
                <div className="space-y-4">
                  <Separator />
                  <div className="text-center">
                    <img
                      src={qrState.uploadQR}
                      alt="Upload QR Code"
                      className="mx-auto border rounded-lg shadow-sm"
                    />
                    <p className="text-sm text-muted-foreground mt-2">
                      Scan with mobile device to access upload page
                    </p>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyUploadQRToClipboard}
                      className="flex-1"
                    >
                      {qrState.copiedUpload ? (
                        <>
                          <Check className="h-4 w-4 mr-2" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4 mr-2" />
                          Copy
                        </>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        downloadQR(
                          qrState.uploadQR,
                          `upload-qr-${wifiForm.ssid}.png`
                        )
                      }
                      className="flex-1"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Instructions Card - Show after QR codes are generated */}
        {(qrState.wifiQR || qrState.uploadQR) && (
          <Card className="mt-8 bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800">
            <CardContent className="pt-6">
              <div className="space-y-4 text-sm">
                <div className="flex items-center gap-2 mb-4">
                  <QrCode className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  <p className="font-medium text-blue-800 dark:text-blue-200">
                    How to use your QR codes:
                  </p>
                </div>
                <ol className="list-decimal list-inside space-y-2 text-blue-700 dark:text-blue-300">
                  <li>
                    <strong>WiFi QR Code:</strong> Users scan this to
                    automatically connect to your WiFi network
                  </li>
                  <li>
                    <strong>Upload QR Code:</strong> Users scan this to go
                    directly to the document upload page (after connecting to
                    WiFi)
                  </li>
                  <li>
                    Share both QR codes with users - they can scan the WiFi code
                    first to connect, then the upload code to access the system
                  </li>
                  <li>
                    Use the copy/download buttons to save or share the QR codes,
                    or print them using the print button below
                  </li>
                </ol>
                <div className="mt-4 p-3 bg-green-100 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                  <p className="text-xs text-green-800 dark:text-green-200">
                    <strong>✓ Enhanced:</strong> Server-side IP detection
                    ensures mobile devices can reliably access your upload page!
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Generate QR Codes Button */}
        <div className="mt-8 text-center">
          <Button
            onClick={generateQRCodes}
            disabled={qrState.isGenerating || !wifiForm.ssid.trim()}
            size="lg"
            className="gap-2"
          >
            {qrState.isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Generating QR Codes...
              </>
            ) : (
              <>
                <QrCode className="h-4 w-4" />
                Generate QR Codes
              </>
            )}
          </Button>
          <p className="text-sm text-muted-foreground mt-2">
            Generate both WiFi connection and document upload QR codes
          </p>
        </div>

        {/* Error Alert */}
        {qrState.error && (
          <Alert variant="destructive" className="mt-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{qrState.error}</AlertDescription>
          </Alert>
        )}

        {/* Print All QR Codes */}
        {(qrState.wifiQR || qrState.uploadQR) && (
          <div className="mt-8 text-center">
            <Button
              onClick={printQRCodes}
              variant="outline"
              size="lg"
              className="gap-2"
            >
              <Printer className="h-4 w-4" />
              Print All QR Codes
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
